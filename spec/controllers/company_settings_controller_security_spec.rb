# ABOUTME: Security test for CompanySettingsController fixing TYM-59 vulnerability
# ABOUTME: Tests that users cannot modify settings for companies they don't have access to

require 'rails_helper'

RSpec.describe CompanySettingsController, type: :controller do
  include JwtHelpers

  let(:company_a) { create(:company, name: "Company A") }
  let(:company_b) { create(:company, name: "Company B") }
  let!(:company_a_setting) { CompanySetting.create!(company: company_a, break_duration: 30) }
  let!(:company_b_setting) { CompanySetting.create!(company: company_b, break_duration: 45) }
  
  let(:owner_user) { create(:user) }
  let(:employee_user) { create(:user) }
  
  let!(:owner_role_a) { create(:company_user_role, user: owner_user, company: company_a, role: create(:role, name: 'owner')) }
  let!(:employee_role_b) { create(:company_user_role, user: employee_user, company: company_b, role: create(:role, name: 'employee')) }

  before do
    # Set the current tenant to company_b (user's primary company)
    ActsAsTenant.current_tenant = company_b
  end

  describe "TYM-59 Security Fix: Company settings access validation" do
    context "when user is owner of company A but tries to access via wrong tenant context" do
      before do
        # User is owner of Company A
        token = jwt_token_for(owner_user)
        request.headers['Authorization'] = "Bearer #{token}"
      end

      it "allows access to company A settings when explicitly requesting company A" do
        get :edit, params: { company_id: company_a.id }
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['company_setting']['break_duration']).to eq(30)
      end

      it "prevents access to company B settings when user is not authorized" do
        get :edit, params: { company_id: company_b.id }
        expect(response).to have_http_status(:redirect)
      end

      it "updates the correct company's settings when explicitly specifying company_id" do
        put :update, params: { 
          company_id: company_a.id, 
          company_setting: { break_duration: 60 } 
        }
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be_truthy
        expect(json_response['company_setting']['break_duration']).to eq(60)
        # Ensure Company B's settings remain unchanged
        expect(company_b_setting.reload.break_duration).to eq(45)
      end

      it "prevents updating company B settings even if user has different tenant context" do
        put :update, params: { 
          company_id: company_b.id, 
          company_setting: { break_duration: 90 } 
        }
        expect(response).to have_http_status(:redirect)
        
        # Ensure Company B's settings remain unchanged
        expect(company_b_setting.reload.break_duration).to eq(45)
      end
    end

    context "when user is employee of company B" do
      before do
        token = jwt_token_for(employee_user)
        request.headers['Authorization'] = "Bearer #{token}"
      end

      it "prevents access to company B settings due to insufficient permissions" do
        get :edit, params: { company_id: company_b.id }
        expect(response).to have_http_status(:redirect)
      end

      it "prevents access to company A settings" do
        get :edit, params: { company_id: company_a.id }
        expect(response).to have_http_status(:redirect)
      end
    end

    context "when company_id parameter is missing or invalid" do
      before do
        token = jwt_token_for(owner_user)
        request.headers['Authorization'] = "Bearer #{token}"
      end

      it "returns 404 for invalid company_id" do
        get :edit, params: { company_id: 99999 }
        expect(response).to have_http_status(:not_found)
      end

      it "raises routing error for nil company_id" do
        expect {
          get :edit, params: { company_id: nil }
        }.to raise_error(ActionController::UrlGenerationError)
      end
    end
  end

  describe "Logo upload security" do
    before do
      token = jwt_token_for(owner_user)
      request.headers['Authorization'] = "Bearer #{token}"
    end

    it "prevents logo upload for unauthorized company" do
      post :update_logo, params: { 
        company_id: company_b.id, 
        logo: nil
      }
      expect(response).to have_http_status(:redirect)
    end
  end
end